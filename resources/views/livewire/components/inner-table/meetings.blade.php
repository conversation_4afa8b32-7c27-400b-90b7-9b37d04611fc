<div>
    {{-- Meetings Listing --}}
    <div class="flex flex-col">
        <div class="flex flex-col">
            <div class="flex flex-col mb-6">
                <flux:heading size="lg" level="1">Meetings</flux:heading>
                <flux:subheading size="md">Here's the meetings for the {{ $resourceNameSingular }}</flux:subheading>
            </div>
            <div class="flex justify-between gap-2">
                <div class="grow">
                    <flux:input wire:model.live="search" placeholder="Search..." clearable size="sm"/>
                </div>
                <div class="flex items-center gap-2">
                    @can('create', $resourceValue)
                        <flux:modal.trigger name="create-meeting">
                            <flux:button size="sm">Create meeting</flux:button>
                        </flux:modal.trigger>
                    @endcan
                </div>
            </div>
        </div>

        <div class="mt-6">
            @if ($this->assets->isEmpty())
                <flux:subheading class="flex justify-center pb-4">No results.</flux:subheading>
            @else
                <flux:table :paginate="$this->assets">
                    <flux:table.columns>
                        <flux:table.column>Name</flux:table.column>
                        <flux:table.column>Type</flux:table.column>
                    </flux:table.columns>

                    <flux:table.rows>
                        @foreach ($this->assets as $asset)
                        <flux:table.row>
                            <flux:table.cell variant="strong">{{ $asset->name }}</flux:table.cell>
                            <flux:table.cell>{{ $asset->type->label() }}</flux:table.cell>

                            <flux:table.cell class="flex justify-end">
                                <flux:button href="{{ $asset->link }}" target="_blank" size="sm" icon="arrow-top-right-on-square" tooltip="Open Link" variant="ghost" x-bind:disabled="! '{{ $asset->link }}'"></flux:button>
                                <flux:button wire:click="downloadFile({{ $asset->id }})" size="sm" icon="folder-arrow-down" tooltip="Download File" variant="ghost" x-bind:disabled="! '{{ $asset->file }}'"></flux:button>

                                <flux:dropdown>
                                    <flux:button size="sm" icon="ellipsis-vertical" tooltip="Actions" variant="ghost"></flux:button>

                                    <x-inner-table.row-crud-menu :item="$asset" />

                                </flux:dropdown>
                            </flux:table.cell>
                        </flux:table.row>
                        @endforeach
                    </flux:table.rows>
                </flux:table>
            @endif
        </div>
    </div>

    {{-- View Meeting - Modal --}}
    <flux:modal name="view-meeting" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">View meeting</flux:heading>
            <flux:subheading>View asset for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <div class="flex flex-col gap-6">
            <flux:input wire:model="form.name" readonly type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" readonly copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:file-upload wire:model="form.file" label="File" description="Upload the file. (max 10MB)" badge="Optional" disabled>
                <flux:file-upload.dropzone
                    inline
                    with-progress
                    heading="Choose a file or drag it here"
                    text="Supports all file types up to 10MB"
                />
            </flux:file-upload>

            @if ($this->form->file)
                <div class="mt-3">
                    <flux:file-item heading="{{ basename($this->form->file) }}" />
                </div>
            @endif
        </div>
    </flux:modal>

    {{-- Create Meeting - Modal --}}
    <flux:modal name="create-meeting" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Create meeting</flux:heading>
            <flux:subheading>Create meeting for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="create" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:file-upload wire:model="form.file" label="File" description="Upload the file. (max 10MB)" badge="Optional">
                <flux:file-upload.dropzone
                    inline
                    with-progress
                    heading="Choose a file or drag it here"
                    text="Supports all file types up to 10MB"
                />
            </flux:file-upload>

            @if ($this->form->file)
                <div class="mt-3">
                    <flux:file-item heading="{{ $this->form->file instanceof \Illuminate\Http\UploadedFile ? $this->form->file->getClientOriginalName() : basename($this->form->file) }}">
                        <x-slot name="actions">
                            <flux:file-item.remove wire:click="unsetFile" />
                        </x-slot>
                    </flux:file-item>
                </div>
            @endif

            <div class="flex">
                <flux:spacer />
                @can('create', $resourceValue)
                    <flux:button size="sm" type="submit" variant="primary">Create meeting</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>

    {{-- Edit Meeting - Modal --}}
    <flux:modal name="edit-meeting" @close="resetForm" variant="flyout" class="w-full md:w-2/3 lg:w-1/3 space-y-12" :dismissible="false">
        <div>
            <flux:heading size="lg">Edit meeting</flux:heading>
            <flux:subheading>Edit meeting for the {{ $resourceNameSingular }}.</flux:subheading>
        </div>

        <form wire:submit="update" class="flex flex-col gap-6">
            <flux:input wire:model="form.name" type="text" placeholder="Mario Rossi" variant="filled" label="Name" description="This will be publicly displayed." />
            <flux:input wire:model="form.link" copyable icon="link" variant="filled" label="Link" badge="Optional" description="This will be publicly displayed." />

            <flux:file-upload wire:model="form.file" label="File" description="Upload the file. (max 10MB)" badge="Optional">
                <flux:file-upload.dropzone
                    inline
                    with-progress
                    heading="Choose a file or drag it here"
                    text="Supports all file types up to 10MB"
                />
            </flux:file-upload>

            @if ($this->form->file)
                <div class="mt-3">
                    <flux:file-item heading="{{ $this->form->file instanceof \Illuminate\Http\UploadedFile ? $this->form->file->getClientOriginalName() : basename($this->form->file) }}">
                        <x-slot name="actions">
                            <flux:file-item.remove wire:click="unsetFile" />
                        </x-slot>
                    </flux:file-item>
                </div>
            @endif

            <div class="flex">
                <flux:spacer />
                @can('update', $resourceValue)
                    <flux:button size="sm" type="submit" variant="primary">Edit meeting</flux:button>
                @endcan
            </div>
        </form>
    </flux:modal>
</div>
