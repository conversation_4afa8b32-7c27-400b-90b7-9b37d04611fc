<flux:main container>
    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Create a new supplier</flux:heading>
    <flux:subheading size="lg" class="mb-6">Here's the supplier info and details</flux:subheading>

    <form wire:submit="save" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Supplier details</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the supplier info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_suppliers_type')
                        <flux:select searchable wire:model="form.type" variant="listbox" placeholder="Choose type..."
                                     label="Type" description="This will be publicly displayed.">
                            @foreach($types as $type)
                                <flux:select.option value="{{ $type->value }}">{{ $type->label() }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    <div></div>
                    @can('view_suppliers_name')
                        <flux:input wire:model="form.name" type="text" placeholder="Supplier Name" variant="filled"
                                    label="Name" description="This will be publicly displayed."/>
                    @endcan
                    @can('view_suppliers_code')
                        <flux:input wire:model="form.code" type="text" placeholder="Supplier Code" variant="filled"
                                    label="Code" description="This will be publicly displayed." badge="Optional"/>
                    @endcan
                    @can('view_suppliers_payment_conditions')
                        <flux:input wire:model="form.payment_conditions" placeholder="Payment Conditions"
                                    variant="filled"
                                    label="Payment Conditions" badge="Optional"
                                    description="This will be publicly displayed."/>
                    @endcan
                    @can('view_suppliers_price_range')
                        <flux:radio.group wire:model="form.price_range" label="Price Range" variant="segmented"
                                          badge="Optional" description="This will be publicly displayed.">
                            @foreach ($priceRanges as $priceRange)
                                <flux:radio value="{{ $priceRange->value }}" label="{{ $priceRange->label() }}"/>
                            @endforeach
                        </flux:radio.group>
                    @endcan
                </div>

                @canany(['view_suppliers_materials', 'view_suppliers_product_types', 'view_suppliers_destination_rooms'])
                    <flux:separator variant="subtle" text="select also" class="my-12"/>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @can('view_suppliers_materials')
                            <flux:field>
                                <flux:label badge="Optional">Materials</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <flux:pillbox wire:model="form.materials" multiple searchable placeholder="Choose materials...">
                                    @foreach($materials as $material)
                                        <flux:pillbox.option value="{{ $material }}" wire:key="{{ $material }}">{{ $material }}</flux:pillbox.option>
                                    @endforeach
                                </flux:pillbox>

                                <flux:error name="form.materials"/>
                            </flux:field>
                        @endcan
                        @can('view_suppliers_product_types')
                            <flux:field>
                                <flux:label badge="Optional">Product Types</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <flux:pillbox wire:model="form.productTypes" multiple searchable placeholder="Choose product types...">
                                    @foreach($productTypes as $productType)
                                        <flux:pillbox.option value="{{ $productType }}" wire:key="{{ $productType }}">{{ $productType }}</flux:pillbox.option>
                                    @endforeach
                                </flux:pillbox>

                                <flux:error name="form.productTypes"/>
                            </flux:field>
                        @endcan
                        @can('view_suppliers_destination_rooms')
                            <flux:field>
                                <flux:label badge="Optional">Destination Rooms</flux:label>
                                <flux:description>This will be publicly displayed.</flux:description>

                                <flux:pillbox wire:model="form.destinationRooms" multiple searchable placeholder="Choose destination rooms...">
                                    @foreach($destinationRooms as $destinationRoom)
                                        <flux:pillbox.option value="{{ $destinationRoom }}" wire:key="{{ $destinationRoom }}">{{ $destinationRoom }}</flux:pillbox.option>
                                    @endforeach
                                </flux:pillbox>

                                <flux:error name="form.destinationRooms"/>
                            </flux:field>
                        @endcan
                    </div>
                @endcanany
            </div>
        </div>

        @can('view_suppliers_notes')
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Extra info</flux:heading>
                    {{-- <flux:subheading size="md" class="mb-6">Here's the brand info and details</flux:subheading> --}}
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 gap-4">
                        <flux:textarea wire:model="form.notes" variant="filled" label="Notes" badge="Optional"
                                       description="This will be publicly displayed."/>
                    </div>
                </div>
            </div>
        @endcan

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-suppliers')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>
    </form>
</flux:main>
