<flux:main container>

    <livewire:components.breadcrumbs/>
    <flux:separator variant="subtle" class="my-4"/>

    <flux:heading size="xl" level="1">Order: {{ $order->code }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">Update the order info and details</flux:subheading>

    <form wire:submit="update" class="flex flex-col gap-12 mt-12">
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
            <div class="col-span-1">
                <flux:heading size="lg" level="1">Quick order</flux:heading>
                <flux:subheading size="md" class="mb-6">Here's the order info and details</flux:subheading>
            </div>

            <div class="col-span-2">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <flux:select searchable wire:model="form.company" variant="listbox"
                                 placeholder="Choose company..." label="Company"
                                 description="This will be publicly displayed.">
                        @foreach($companies as $company)
                            <flux:select.option value="{{ $company->value }}">{{ $company->label() }}</flux:select.option>
                        @endforeach
                    </flux:select>
                    <div></div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @can('view_orders_date')
                        <flux:date-picker wire:model.live="form.date" label="Date"
                                          description="This will be publicly displayed."/>
                    @endcan
                    @can('view_orders_description')
                        <flux:input wire:model="form.description" type="text" placeholder="Description" variant="filled"
                                    label="Description" description="This will be publicly displayed."/>
                    @endcan
                    @can('view_orders_internal_referent_id')
                        <flux:select searchable wire:model="form.internal_referent_id" variant="listbox" clearable
                                     placeholder="Choose internal referent..." label="Internal Referent"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($internalReferents as $internalReferent)
                                <flux:select.option
                                    value="{{ $internalReferent->id }}">{{ $internalReferent->first_name . ' ' . $internalReferent->last_name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                    @can('view_orders_area_manager_id')
                        <flux:select searchable wire:model="form.area_manager_id" variant="listbox" clearable
                                     placeholder="Choose area manager..." label="Area Manager"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($areaManagers as $areaManager)
                                <flux:select.option
                                    value="{{ $areaManager->id }}">{{ $areaManager->first_name . ' ' . $areaManager->last_name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    @endcan
                </div>
            </div>
        </div>

        @canany(['view_orders_order_code_type', 'view_orders_order_code_region', 'view_orders_order_code_progressive', 'view_orders_client_id', 'view_orders_partner_id', 'view_orders_payment_term_id', 'view_orders_vat_type', 'view_orders_invoicing_address_id', 'view_orders_shipping_address_id'])
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Trade order</flux:heading>
                    <flux:subheading size="md" class="mb-6">Here's the order info and details</flux:subheading>
                </div>

                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @can('view_orders_order_code_type')
                            <flux:select searchable wire:model.live="form.order_code_type" variant="listbox" clearable
                                         placeholder="Choose type..." label="Order Code Type"
                                         description="This will be publicly displayed." badge="Optional"
                                         x-bind:disabled="$wire.form.project_id">
                                @foreach($orderCodeTypes as $orderCodeType)
                                    <flux:select.option
                                        value="{{ $orderCodeType->value }}">{{ $orderCodeType->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_order_code_region')
                            <flux:select searchable wire:model.live="form.order_code_region" variant="listbox" clearable
                                         placeholder="Choose region..." label="Order Code Region"
                                         description="This will be publicly displayed." badge="Optional"
                                         x-bind:disabled="$wire.form.project_id">
                                @foreach($orderCodeRegions as $orderCodeRegion)
                                    <flux:select.option
                                        value="{{ $orderCodeRegion->value }}">{{ $orderCodeRegion->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_order_code_progressive')
                            <flux:input wire:model="form.order_code_progressive" readonly type="number" icon="hashtag"
                                        placeholder="0" variant="filled" label="Progressive"
                                        description="Automatically generated." badge="Readonly"
                                        x-bind:disabled="$wire.form.project_id"/>
                        @endcan
                    </div>

                    <flux:separator variant="subtle" text="select also" class="my-12"/>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @can('view_orders_client_id')
                            <flux:select searchable wire:model.live="form.client_id" variant="listbox" clearable
                                         placeholder="Choose client..." label="Client"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($clients as $client)
                                    <flux:select.option
                                        value="{{ $client->id }}">{{ $client->company }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_partner_id')
                            <flux:select searchable wire:model="form.partner_id" variant="listbox" clearable
                                         placeholder="Choose partner..." label="Partner"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($partners as $partner)
                                    <flux:select.option
                                        value="{{ $partner->id }}">{{ $partner->company }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_payment_term_id')
                            <flux:select searchable wire:model="form.payment_term_id" variant="listbox" clearable
                                         placeholder="Choose payment terms..." label="Payment Terms"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($paymentTerms as $paymentTerm)
                                    <flux:select.option
                                        value="{{ $paymentTerm->id }}">{{ $paymentTerm->code . ' - ' . $paymentTerm->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_vat_type')
                            <flux:select searchable wire:model="form.vat_type" variant="listbox" clearable
                                         placeholder="Choose VAT type..." label="VAT Type"
                                         description="This will be publicly displayed.">
                                @foreach($vatTypes as $vatType)
                                    <flux:select.option
                                        value="{{ $vatType->value }}">{{ $vatType->label() }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_invoicing_address_id')
                            <flux:select searchable wire:model="form.invoicing_address_id" variant="listbox" clearable
                                         placeholder="Choose address..." label="Invoicing Address"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($invoicingAddresses as $invoicingAddress)
                                    <flux:select.option
                                        value="{{ $invoicingAddress->id }}">{{ $invoicingAddress->company . ' - ' . $invoicingAddress->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                        @can('view_orders_shipping_address_id')
                            <flux:select searchable wire:model="form.shipping_address_id" variant="listbox" clearable
                                         placeholder="Choose address..." label="Shipping Address"
                                         description="This will be publicly displayed." badge="Optional">
                                @foreach($shippingAddresses as $shippingAddress)
                                    <flux:select.option
                                        value="{{ $shippingAddress->id }}">{{ $shippingAddress->company . ' - ' . $shippingAddress->name }}</flux:select.option>
                                @endforeach
                            </flux:select>
                        @endcan
                    </div>
                </div>
            </div>
        @endcanany

        @can('view_orders_project_id')
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Project details</flux:heading>
                    <flux:subheading size="md" class="mb-6">Here's the project info and details</flux:subheading>
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <flux:select searchable wire:model.live="form.project_id" variant="listbox" clearable
                                     placeholder="Choose project..." label="Project"
                                     description="This will be publicly displayed." badge="Optional">
                            @foreach($projects as $project)
                                <flux:select.option value="{{ $project->id }}">{{ $project->name }}</flux:select.option>
                            @endforeach
                        </flux:select>
                    </div>
                </div>
            </div>
        @endcan

        @can('view_orders_confirmation_file')
            <flux:separator variant="subtle"/>

            <div class="grid grid-cols-1 xl:grid-cols-3 gap-2">
                <div class="col-span-1">
                    <flux:heading size="lg" level="1">Confirmation file</flux:heading>
                    <flux:subheading size="md" class="mb-6">Here's the confirmation document</flux:subheading>
                </div>
                <div class="col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <flux:input wire:model="form.confirmation_file" type="file" size="sm" label="Confirmation File"
                                    description="Upload the confirmation file in PDF. (max 10MB)" badge="Optional"/>

                        @if ($this->form->confirmation_file)
                            <x-files.preview-card unsetAction="unsetFile"/>
                        @endif
                    </div>
                </div>
            </div>
        @endcan

        <div class="flex justify-end gap-4">
            <flux:button @click="$dispatch('redirect-to-orders')" size="sm">Cancel</flux:button>
            <flux:button size="sm" type="submit" variant="primary">Save</flux:button>
        </div>

    </form>

</flux:main>
