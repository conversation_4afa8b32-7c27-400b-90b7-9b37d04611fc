<?php

namespace App\Livewire\Pages\Orders;

use App\Enums\AddressTypes;
use App\Enums\Companies;
use App\Enums\OrderCodeRegions;
use App\Enums\OrderCodeTypes;
use App\Enums\VatTypes;
use App\Livewire\Forms\OrderForm;
use App\Models\Address;
use App\Models\Client;
use App\Models\Order\Order;
use App\Models\Partner;
use App\Models\PaymentTerm;
use App\Models\User;
use Carbon\Carbon;
use Flux\Flux;
use Livewire\Component;
use Livewire\WithFileUploads;

class Edit extends Component
{
    use WithFileUploads;

    public Order $order;

    public OrderForm $form;

    public function mount(Order $order)
    {
        $this->form->setOrder($order);
    }

    public function render()
    {
        // Get the client
        if ($this->form->client_id) {
            $client = Client::find($this->form->client_id);
        } else {
            $client = null;
        }

        return view('livewire.pages.orders.edit', [
            'internalReferents' => User::query()->where('is_employee', true)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', true)->orderBy('id', 'desc')->get(),
            'clients' => Client::orderBy('id', 'desc')->get(),
            'partners' => $this->form->client_id
                ? Partner::where('id', $client?->partner_id)->get()
                : [],
            'invoicingAddresses' => $this->form->client_id
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Invoicing->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'shippingAddresses' => $this->form->client_id
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Shipping->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'vatTypes' => VatTypes::cases(),
            'orderCodeTypes' => OrderCodeTypes::cases(),
            'orderCodeRegions' => OrderCodeRegions::cases(),
            'companies' => Companies::cases(),
            'projects' => $client?->projects ?? [],
        ]);
    }

    public function update(): void
    {
        $this->form->update();

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been updated.'
        );

        $this->redirectRoute('orders.index', navigate: true);
    }

    public function updatedFormClientId(): void
    {
        $this->form->partner_id = null;
        $this->form->invoicing_address_id = null;
        $this->form->shipping_address_id = null;
    }

    public function updatedFormOrderCodeType(): void
    {
        $this->form->order_code_progressive = null;
        $this->form->order_code_progressive = $this->getNextOrderCodeProgressive(
            $this->form->order_code_type,
            $this->form->order_code_region
        );
    }

    public function updatedFormOrderCodeRegion(): void
    {
        $this->form->order_code_progressive = null;
        $this->form->order_code_progressive = $this->getNextOrderCodeProgressive(
            $this->form->order_code_type,
            $this->form->order_code_region
        );
    }

    public function updatedFormDate(): void
    {
        $this->form->order_code_date = $this->form->date;
        $this->form->order_code_progressive = $this->getNextOrderCodeProgressive(
            $this->form->order_code_type,
            $this->form->order_code_region
        );
    }

    public function updatedFormProjectId(): void
    {
        // Reset the order code values when the project id changes
        $this->form->order_code_date = null;
        $this->form->order_code_progressive = null;
        $this->form->order_code_type = null;
        $this->form->order_code_region = null;

        // Set the project id to null if empty
        $this->form->project_id = $this->form->project_id ?: null;
    }

    public function unsetFile(): void
    {
        $this->form->confirmation_file = null;
    }

    /**
     * Get the next order code progressive
     */
    private function getNextOrderCodeProgressive($type, $region): ?int
    {
        $year = Carbon::parse($this->form->date)->format('Y');

        // Set the order code progressive based on the order code type and region
        if ($this->form->date && $this->form->order_code_type && $this->form->order_code_region) {
            return (Order::whereYear('date', $year)
                ->where('order_code_type', $type)
                ->where('order_code_region', $region)
                ->max('order_code_progressive') ?? 0) + 1;
        } else {
            return null;
        }
    }
}
