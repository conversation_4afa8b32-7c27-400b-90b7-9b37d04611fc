<?php

namespace App\Livewire\Pages\Suppliers;

use App\Enums\BrandPriceRange;
use App\Enums\SupplierTypes;
use App\Livewire\Forms\SupplierForm;
use Flux\Flux;
use Livewire\Component;
use Spatie\Tags\Tag;

class Create extends Component
{
    public SupplierForm $form;

    public function render()
    {
        $materials = Tag::where('type', 'material')->pluck('name')->toArray();
        $productTypes = Tag::where('type', 'product_type')->pluck('name')->toArray();
        $destinationRooms = Tag::where('type', 'destination_room')->pluck('name')->toArray();

        return view('livewire.pages.suppliers.create', [
            'priceRanges' => BrandPriceRange::cases(),
            'types' => SupplierTypes::cases(),
            'materials' => $materials,
            'productTypes' => $productTypes,
            'destinationRooms' => $destinationRooms,
        ]);
    }

    public function save(): void
    {
        $this->form->store();

        Flux::toast(
            variant: 'success',
            text: 'The SUPPLIER has been created.'
        );

        $this->redirectRoute('suppliers.index', navigate: true);
    }
}
