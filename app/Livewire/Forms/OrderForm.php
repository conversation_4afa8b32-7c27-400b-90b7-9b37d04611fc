<?php

namespace App\Livewire\Forms;

use App\Enums\VatTypes;
use App\Models\Order\Order;
use Illuminate\Http\UploadedFile;
use Livewire\Form;
use Livewire\WithFileUploads;

class OrderForm extends Form
{
    use WithFileUploads;

    public $isEditMode = false;

    public ?Order $order;

    public $project_id;

    public $date;

    public $description;

    public $order_code_progressive;

    public $order_code_date;

    public $order_code_type;

    public $order_code_region;

    public $internal_referent_id;

    public $area_manager_id;

    public $client_id;

    public $partner_id;

    public $invoicing_address_id;

    public $shipping_address_id;

    public $payment_term_id;

    public $vat_type;

    public $company;

    public $confirmation_file;

    public function rules()
    {
        return [
            'project_id' => 'nullable|exists:projects,id',

            'date' => 'required|date',
            'description' => 'required|string',

            'order_code_date' => 'nullable|date',
            'order_code_type' => 'nullable',
            'order_code_region' => 'nullable',

            'internal_referent_id' => 'nullable|exists:users,id',
            'area_manager_id' => 'nullable|exists:users,id',
            'client_id' => 'nullable|exists:clients,id',
            'partner_id' => 'nullable|exists:partners,id',
            'invoicing_address_id' => 'nullable|exists:addresses,id',
            'shipping_address_id' => 'nullable|exists:addresses,id',
            'payment_term_id' => 'nullable|exists:payment_terms,id',
            'vat_type' => 'nullable',
            'company' => 'required|string',

            'confirmation_file' => $this->confirmation_file instanceof UploadedFile ? 'nullable|mimetypes:application/pdf|max:10240' : 'nullable|string',
        ];
    }

    public function setOrder(Order $order)
    {
        $this->isEditMode = true;
        $this->order = $order;

        $this->project_id = $order->project_id ?: null;

        $this->date = $order->date ? $order->date->format('Y-m-d') : null;
        $this->description = $order->description;

        $this->order_code_progressive = $order->order_code_progressive;
        $this->order_code_date = $order->order_code_date ? $order->order_code_date->format('Y-m-d') : null;
        $this->order_code_type = $order->order_code_type;
        $this->order_code_region = $order->order_code_region;

        $this->internal_referent_id = $order->internal_referent_id ?: null;
        $this->area_manager_id = $order->area_manager_id ?: null;
        $this->client_id = $order->client_id ?: null;
        $this->partner_id = $order->partner_id ?: null;
        $this->invoicing_address_id = $order->invoicing_address_id ?: null;
        $this->shipping_address_id = $order->shipping_address_id ?: null;
        $this->payment_term_id = $order->payment_term_id ?: null;
        $this->vat_type = $order->vat_type ?: null;
        $this->company = $order->company?->value;

        $this->confirmation_file = $order->confirmation_file;
    }

    public function store()
    {
        $this->validate();

        $order = new Order;
        $order->project_id = $this->project_id ?: null;

        $order->date = $this->date;
        $order->description = $this->description;

        $order->order_code_progressive = $this->order_code_progressive;
        $order->order_code_date = $this->order_code_date;
        $order->order_code_type = $this->order_code_type;
        $order->order_code_region = $this->order_code_region;

        $order->internal_referent_id = $this->internal_referent_id ?: null;
        $order->area_manager_id = $this->area_manager_id ?: null;
        $order->client_id = $this->client_id ?: null;
        $order->partner_id = $this->partner_id ?: null;
        $order->invoicing_address_id = $this->invoicing_address_id ?: null;
        $order->shipping_address_id = $this->shipping_address_id ?: null;
        $order->payment_term_id = $this->payment_term_id ?: null;
        $order->vat_type = $this->vat_type ?: VatTypes::Standard->value;
        $order->company = $this->company;

        if ($this->confirmation_file instanceof UploadedFile) {
            $order->confirmation_file = $this->confirmation_file->store('orders/confirmations', config('filesystems.private'));
        }

        $order->save();

        $this->reset();
    }

    public function update()
    {
        $this->validate();

        $this->order->project_id = $this->project_id ?: null;

        $this->order->date = $this->date;
        $this->order->description = $this->description;

        $this->order->order_code_progressive = $this->order_code_progressive;
        $this->order->order_code_date = $this->order_code_date;
        $this->order->order_code_type = $this->order_code_type;
        $this->order->order_code_region = $this->order_code_region;

        $this->order->internal_referent_id = $this->internal_referent_id ?: null;
        $this->order->area_manager_id = $this->area_manager_id ?: null;
        $this->order->client_id = $this->client_id ?: null;
        $this->order->partner_id = $this->partner_id ?: null;
        $this->order->invoicing_address_id = $this->invoicing_address_id ?: null;
        $this->order->shipping_address_id = $this->shipping_address_id ?: null;
        $this->order->payment_term_id = $this->payment_term_id ?: null;
        $this->order->vat_type = $this->vat_type ?: VatTypes::Standard->value;
        $this->order->company = $this->company;

        if ($this->confirmation_file instanceof UploadedFile) {
            $this->order->confirmation_file = $this->confirmation_file->store('orders/confirmations', config('filesystems.private'));
        } elseif ($this->confirmation_file == null) {
            $this->order->confirmation_file = null;
        }

        $this->order->save();

        $this->reset();
    }
}
