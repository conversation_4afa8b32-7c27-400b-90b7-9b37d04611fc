<?php

namespace App\Actions\Orders\Statuses;

use App\Models\Order\Order;
use App\Enums\OrderStatuses;
use Illuminate\Support\Facades\DB;

class ReOpen
{
    public static function run(Order $order): array
    {
        DB::beginTransaction();

        try {
            // Check if the order is in SUBMITTED status
            if ($order->status !== OrderStatuses::Submitted && $order->status !== OrderStatuses::Rejected && $order->status !== OrderStatuses::Approved) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'The ORDER can only be re-opened if it is in SUBMITTED status.'
                ];
            }

            // Clean the "frozen" fields in order rows to restore dynamic behavior
            $order->orderRows()->each(function ($row) {
                $updateData = [];

                // Clean SKU (will be calculated dynamically from product)
                $updateData['sku'] = null;

                // Clean description (will be taken from product)
                $updateData['description'] = null;

                // Clean purchasing price (will be taken from product)
                $updateData['purchasing_price'] = null;

                // Clean selling price only if there's no override
                if (!$row->selling_price_override) {
                    $updateData['selling_price'] = null;
                }

                // Clean discount only if there's no override
                if (!isset($row->discount_override)) {
                    $updateData['discount'] = null;
                }

                $row->update($updateData);
            });

            // Update the order status back to OPEN
            $order->update([
                'status' => OrderStatuses::Open->value
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'The ORDER has been re-opened.'
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'An error occurred while re-opening the order.'
            ];
        }
    }
}
